import React, { useState, useEffect } from "react";
import { GlobalContext, showToast } from "Src/globalContext";
import CustomSelect2 from "Components/CustomSelect2";
import moment from "moment";
import {
  ArrowLeft,
  Plus,
  Trash2,
  PlusCircle,
  Download,
  Upload,
  X,
  FileText,
} from "lucide-react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import MkdSDK from "Utils/MkdSDK";
import { SingleDatePicker } from "react-dates";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import { PDFDownloadLink } from "@react-pdf/renderer";
import InvoiceQuotePDF from "./InvoiceQuotePDF";
import { uploadS3FilesAPI } from "Src/services/workOrderService";
import { getAllMembersForManager } from "Src/services/managerServices";
import {
  retrieveAllForClientForManager,
  retrieveAllForMixTypeForManager,
  retrieveAllForMixSeasonForManager,
} from "Src/services/managerServices";

// Status Confirmation Modal Component
const StatusConfirmationModal = ({ isOpen, onClose, onConfirm, status }) => {
  if (!isOpen) return null;

  const statusText =
    status === "3"
      ? "Paid in Full"
      : status === "2"
      ? "Deposit Paid"
      : "Unpaid";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-md rounded-lg bg-boxdark p-6 shadow-lg">
        <h3 className="mb-4 text-xl font-semibold text-white">
          Confirm Status Change
        </h3>
        <p className="mb-6 text-bodydark2">
          Are you sure you want to change the status to{" "}
          <strong>{statusText}</strong>?
          {status === "3" && (
            <span className="mt-2 block text-warning">
              This will mark the item as fully paid.
            </span>
          )}
          {status === "2" && (
            <span className="mt-2 block text-warning">
              This will mark the item as deposit paid.
            </span>
          )}
        </p>
        <div className="flex justify-end gap-4">
          <button
            onClick={onClose}
            className="rounded bg-boxdark-2 px-4 py-2 text-white hover:bg-opacity-90"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

const ManagerEditInvoiceComponent = ({
  id,
  onClose,
  userDetails,
  onSubmit,
  onResend,
}) => {
  const [invoiceData, setInvoiceData] = useState({
    notes: "",
    termsAndConditions: "",
    depositAmount: 0,
    depositPercentage: 30,
    items: [],
  });
  const [focusedInput, setFocusedInput] = React.useState({
    date: null,
    dueDate: null,
    musicSurveyDue: {},
    submission: {},
    estimatedCompletion: {},
  });
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedClientId, setSelectedClientId] = useState("");
  const [loading, setLoading] = useState(true);
  const [producers, setProducers] = useState([]);
  const [clients, setClients] = useState([]);
  const [mixTypes, setMixTypes] = useState([]);
  const [producerMixTypes, setProducerMixTypes] = useState({});
  const [mixSeasons, setMixSeasons] = useState([]);
  const [producerMixSeasons, setProducerMixSeasons] = useState({});
  const [companyInfo, setCompanyInfo] = useState(null);
  const [invoiceDates, setInvoiceDates] = useState({
    invoiceDate: new Date().toISOString().split("T")[0],
    dueDate: "",
  });

  const [isQuote, setIsQuote] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [newStatus, setNewStatus] = useState("");
  const [uploadedChecks, setUploadedChecks] = useState([]);
  const [isUploadingCheck, setIsUploadingCheck] = useState(false);

  // SunEditor button list
  const buttonList = {
    complex: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["removeFormat"],
      ["fontColor", "hiliteColor"],
      ["indent", "outdent"],
      ["align", "horizontalRule", "list", "table"],
      ["link"],
      ["fullScreen", "showBlocks", "codeView"],
      ["preview"],
    ],
  };

  // Get SunEditor instance
  const [editorInstance, setEditorInstance] = useState(null);

  const getSunEditorInstance = (sunEditor) => {
    setEditorInstance(sunEditor);
  };

  // Fetch company info
  const fetchCompanyInfo = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error) {
        console.log("Company info:", response);
        setCompanyInfo(response);
      } else {
        console.error(
          "Error fetching company info:",
          response.message || "Unknown error"
        );
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  // Fetch producers, their clients, and mix types
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch company info
        await fetchCompanyInfo();

        const result = await getAllMembersForManager(
          localStorage.getItem("user")
        );
        if (!result.error && result.list.length > 0) {
          const producerList = result.list.map((producer) => ({
            value: producer.id,
            label: producer.first_name + " " + producer.last_name,
          }));
          setProducers(producerList);

          // Fetch clients for all producers
          const clientResult = await retrieveAllForClientForManager(1, 1000, {
            member_ids: producerList.map((p) => p.value),
          });

          if (!clientResult.error) {
            const uniqueClients = Array.from(
              new Map(
                clientResult.list.map((client) => [client.id, client])
              ).values()
            );
            setClients(uniqueClients);
          }

          // Fetch mix types for all producers
          const mixTypeResult = await retrieveAllForMixTypeForManager(1, 1000, {
            member_ids: producerList.map((p) => p.value),
          });

          if (!mixTypeResult.error) {
            // Create a map of producer IDs to their mix types
            const mixTypesByProducer = {};
            mixTypeResult.list.forEach((mixType) => {
              if (!mixTypesByProducer[mixType.user_id]) {
                mixTypesByProducer[mixType.user_id] = [];
              }
              mixTypesByProducer[mixType.user_id].push(mixType);
            });
            setProducerMixTypes(mixTypesByProducer);

            // Create a flat list of all mix types with producer labels
            const allMixTypes = mixTypeResult.list.map((mixType) => {
              const producer = producerList.find(
                (p) => p.value === mixType.user_id
              );
              return {
                ...mixType,
                member_id: mixType.user_id,
                displayName: `${mixType.name} > ${
                  producer?.label || "Unknown Producer"
                }`,
                price: parseFloat(mixType.price) || 0,
              };
            });
            setMixTypes(allMixTypes);
          }

          // Fetch mix seasons for all producers
          try {
            const mixSeasonResult = await retrieveAllForMixSeasonForManager(
              1,
              1000,
              {
                member_ids: producerList.map((p) => p.value),
              }
            );

            if (!mixSeasonResult.error) {
              // Create a map of producer IDs to their mix seasons
              const seasonsByProducer = {};
              const allSeasons = [];

              // Process the mix seasons and organize by producer
              mixSeasonResult.list.forEach((season) => {
                const producerId = season.user_id;

                // Only include active seasons (status === 1)
                if (season.status === 1) {
                  if (!seasonsByProducer[producerId]) {
                    seasonsByProducer[producerId] = [];
                  }

                  seasonsByProducer[producerId].push(season);
                  allSeasons.push(season);
                }
              });

              setProducerMixSeasons(seasonsByProducer);
              setMixSeasons(allSeasons);
            }
          } catch (error) {
            console.error("Error fetching mix seasons:", error);
            showToast(
              globalDispatch,
              "Failed to fetch mix seasons",
              3000,
              "error"
            );
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        showToast(globalDispatch, "Failed to fetch data", 3000, "error");
      }
    };

    fetchData();
  }, [globalDispatch]);

  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();
        const result = await sdk.getInvoiceById(id);

        setSelectedClientId(result.invoice.client_id?.toString() || "");

        // Collect all unique producer IDs from the invoice items
        const producerIds = new Set();
        result.items.forEach((item) => {
          if (item.producer) {
            producerIds.add(item.producer);
          }
        });

        // Fetch mix seasons and mix types for each producer
        const promises = [];
        producerIds.forEach((producerId) => {
          // Fetch mix seasons and mix types for each producer
          const producerIdInt = parseInt(producerId);
          if (producerIdInt) {
            // Check if we already have the data for this producer
            if (!producerMixSeasons[producerIdInt]) {
              promises.push(
                retrieveAllForMixSeasonForManager(1, 1000, {
                  member_ids: [producerIdInt],
                }).then((response) => {
                  if (!response.error) {
                    // Create a map of producer IDs to their mix seasons
                    const seasonsByProducer = { ...producerMixSeasons };

                    // Process the mix seasons and organize by producer
                    response.list.forEach((season) => {
                      const pid = season.user_id;

                      // Only include active seasons (status === 1)
                      if (season.status === 1) {
                        if (!seasonsByProducer[pid]) {
                          seasonsByProducer[pid] = [];
                        }

                        seasonsByProducer[pid].push(season);
                      }
                    });

                    setProducerMixSeasons(seasonsByProducer);
                  }
                })
              );
            }

            // Check if we already have mix types for this producer
            if (!producerMixTypes[producerIdInt]) {
              promises.push(
                retrieveAllForMixTypeForManager(1, 1000, {
                  member_ids: [producerIdInt],
                }).then((response) => {
                  if (!response.error) {
                    // Create a map of producer IDs to their mix types
                    const mixTypesByProducer = { ...producerMixTypes };

                    response.list.forEach((mixType) => {
                      const pid = mixType.user_id;
                      if (!mixTypesByProducer[pid]) {
                        mixTypesByProducer[pid] = {
                          mixTypes: [],
                        };
                      }
                      mixTypesByProducer[pid].mixTypes.push(mixType);
                    });

                    setProducerMixTypes(mixTypesByProducer);
                  }
                })
              );
            }
          }
        });

        await Promise.all(promises);

        const formattedData = {
          notes: result.invoice.notes || "",
          termsAndConditions: result.invoice.terms_and_conditions || "",
          depositAmount: parseFloat(result.invoice.deposit_amount) || 0,
          depositPercentage:
            parseFloat(result.invoice.deposit_percentage) || 30,
          items: result.items.map((item) => {
            // Check if this is a special row
            const isSpecialRow = !item.mix_date && !item.team_name;

            if (isSpecialRow) {
              return {
                id: item.id,
                name: item.description || "Additional Charge",
                price: parseFloat(item.price) || 0,
                quantity: item.quantity || 1,
                discount: parseFloat(item.discount) || 0,
                amount: parseFloat(item.total) || 0,
                isSpecial: true,
                isSpecialRow: true,
                specialType: "additional",
                // Empty values for required fields
                mixDate: "",
                teamName: "",
                division: "",
                musicSurveyDue: "",
                routineSubmissionDue: "",
                estimatedCompletion: "",
                mixSeason: "",
              };
            } else {
              return {
                id: item.id,
                mixDate: item.mix_date
                  ? moment(item.mix_date).format("YYYY-MM-DD")
                  : "",
                producer: item.producer?.toString() || "",
                mixType: item.mix_type_id?.toString() || "",
                teamName: item.team_name || "",
                division: item.division || "",
                musicSurveyDue: item.music_survey_due
                  ? moment(item.music_survey_due).format("YYYY-MM-DD")
                  : "",
                routineSubmissionDue: item.routine_submission_due
                  ? moment(item.routine_submission_due).format("YYYY-MM-DD")
                  : "",
                estimatedCompletion: item.estimated_completion
                  ? moment(item.estimated_completion).format("YYYY-MM-DD")
                  : "",
                price: parseFloat(item.price) || 0,
                quantity: item.quantity || 1,
                discount: parseFloat(item.discount) || 0,
                mixSeason: item.mix_season_id
                  ? item.mix_season_id.toString()
                  : "",
                amount: parseFloat(item.total) || 0,
                isSpecialRow: false,
                isSpecial: false,
              };
            }
          }),
        };

        setInvoiceData(formattedData);

        // Load existing checks if they exist
        if (result.invoice.checks) {
          try {
            const existingChecks = JSON.parse(result.invoice.checks);
            setUploadedChecks(existingChecks);
          } catch (error) {
            console.error("Error parsing existing checks:", error);
            setUploadedChecks([]);
          }
        }

        setInvoiceDates({
          invoiceDate: moment(result.invoice.invoice_date).format("YYYY-MM-DD"),
          dueDate: result.invoice.due_date
            ? moment(result.invoice.due_date).format("YYYY-MM-DD")
            : "",
        });
      } catch (error) {
        console.error("Error fetching invoice:", error);
        showToast(
          globalDispatch,
          "Failed to fetch invoice details",
          3000,
          "error"
        );
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchInvoiceDetails();
    }
  }, [id, globalDispatch, producerMixSeasons, producerMixTypes]);

  // Update available mix types when producer is selected
  const getAvailableMixTypes = (producerId) => {
    if (!producerId) return [];

    const producerIdInt = parseInt(producerId);
    const producerMixTypesList = producerMixTypes[producerIdInt];

    // Check if producerMixTypesList is an array (direct list of mix types)
    if (Array.isArray(producerMixTypesList)) {
      return producerMixTypesList;
    }
    // If it's an object with mixTypes property
    else if (
      producerMixTypesList &&
      Array.isArray(producerMixTypesList.mixTypes)
    ) {
      return producerMixTypesList.mixTypes;
    }

    // Fallback to filtering from global mix types
    return mixTypes.filter((mt) => mt.user_id === producerIdInt);
  };

  const calculateDates = (mixDate, index) => {
    if (!mixDate) return;

    const mix = moment(mixDate);
    const surveyDue = mix.clone().subtract(14, "days");
    const submissionDue = mix.clone().subtract(7, "days");
    const estimatedCompletion = mix.clone().add(7, "days");

    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      newItems[index] = {
        ...newItems[index],
        musicSurveyDue: surveyDue.format("YYYY-MM-DD"),
        routineSubmissionDue: submissionDue.format("YYYY-MM-DD"),
        estimatedCompletion: estimatedCompletion.format("YYYY-MM-DD"),
      };
      return { ...prev, items: newItems };
    });
  };

  const handleAddItem = () => {
    setInvoiceData((prev) => {
      // Count only non-special items to determine the next team number
      const regularItems = prev.items.filter(
        (item) => !item.isSpecial && !item.isSpecialRow
      );
      const nextTeamNumber = regularItems.length + 1;

      return {
        ...prev,
        items: [
          ...prev.items,
          {
            mixDate: "",
            producer: "",
            mixType: "",
            teamName: `Team ${nextTeamNumber}`,
            division: "TBD",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            price: 0,
            quantity: 1,
            discount: 0,
            mixSeason: "",
            isSpecialRow: false,
            isSpecial: false,
          },
        ],
      };
    });
  };

  const handleAddSpecialRow = () => {
    setInvoiceData((prev) => {
      return {
        ...prev,
        items: [
          ...prev.items,
          {
            name: "Additional Charge",
            price: 0,
            quantity: 1,
            discount: 0,
            specialType: "additional",
            isSpecial: true,
            isSpecialRow: true,
            // Empty values for required fields
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            mixSeason: "",
          },
        ],
      };
    });
  };

  const handleRemoveItem = (indexToRemove) => {
    setInvoiceData((prev) => {
      const newItems = prev.items.filter((_, index) => index !== indexToRemove);

      // Count regular items and renumber them
      let regularItemCount = 0;
      const updatedItems = newItems.map((item) => {
        if (!item.isSpecial && !item.isSpecialRow) {
          regularItemCount++;
          if (item.teamName && item.teamName.startsWith("Team ")) {
            return { ...item, teamName: `Team ${regularItemCount}` };
          }
        }
        return item;
      });

      return { ...prev, items: updatedItems };
    });
  };

  // Function to handle status update
  const handleStatusUpdate = async (itemId, newStatus) => {
    try {
      const sdk = new MkdSDK();

      // Get the current invoice data to update
      const currentInvoice = await sdk.getInvoiceById(id);
      if (currentInvoice.error) {
        throw new Error("Failed to fetch current invoice data");
      }

      // Find the item to update
      const itemToUpdate = currentInvoice.items.find(
        (item) => item.id === itemId
      );
      if (!itemToUpdate) {
        throw new Error("Invoice item not found");
      }

      // Update the status in the item
      itemToUpdate.status = newStatus;

      // Prepare the full invoice update payload
      const clientData = clients.find(
        (c) => c.id.toString() === selectedClientId
      );

      const payload = {
        clientId: parseInt(selectedClientId),
        clientEmail: clientData?.email || "",
        clientName: clientData?.program || "",
        programName: clientData?.program || "",
        invoiceDate: invoiceDates.invoiceDate,
        dueDate: invoiceDates.dueDate || null,
        items: currentInvoice.items.map((item) => {
          // Keep the existing item data but update the status for the specific item
          return {
            ...item,
            status: item.id === itemId ? newStatus : item.status,
          };
        }),
        depositPercentage: invoiceData.depositPercentage,
        notes: invoiceData.notes,
        termsAndConditions: invoiceData.termsAndConditions,
      };

      // Update the entire invoice
      await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${id}`,
        payload,
        "PUT"
      );

      // Show success message
      showToast(globalDispatch, "Status updated successfully", 3000, "success");

      // Close the modal
      setIsStatusModalOpen(false);
      setSelectedItem(null);
      setNewStatus("");

      // Update the local state
      const updatedItems = invoiceData.items.map((item) => {
        if (item.id === itemId) {
          return { ...item, status: newStatus };
        }
        return item;
      });

      setInvoiceData((prev) => ({
        ...prev,
        items: updatedItems,
      }));
    } catch (error) {
      console.error("Error updating status:", error);
      showToast(globalDispatch, "Failed to update status", 3000, "error");
    }
  };

  const handleItemChange = (index, field, value) => {
    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      const item = newItems[index];

      if (field === "teamName") {
        if (value !== "" || !item.teamName?.startsWith("Team ")) {
          item.teamName = value;
        }
      } else if (field === "division") {
        if (value !== "" || item.division !== "TBD") {
          item.division = value;
        }
      } else if (field === "mixSeason") {
        item.mixSeason = value;
        // Auto-set the discount if a season is selected
        if (value) {
          // Try to find the season in the producer-specific mix seasons first
          const producerId = parseInt(item.producer);
          let selectedSeason = null;

          if (producerId && producerMixSeasons[producerId]) {
            // Look in producer-specific mix seasons
            selectedSeason = producerMixSeasons[producerId].find(
              (s) => s.id === parseInt(value)
            );
          }

          // If not found, fall back to the global mix seasons
          if (!selectedSeason) {
            selectedSeason = mixSeasons.find((s) => s.id === parseInt(value));
          }

          if (selectedSeason) {
            item.discount = selectedSeason.discount || 0;
          }
        }
      } else if (field === "producer") {
        item.producer = value;

        // When producer changes, reset mix type and mix season
        item.mixType = "";
        item.mixSeason = "";

        // Fetch mix seasons and mix types for the selected producer if needed
        const producerId = parseInt(value);
        if (
          producerId &&
          (!producerMixSeasons[producerId] || !producerMixTypes[producerId])
        ) {
          // Fetch mix seasons for this producer
          retrieveAllForMixSeasonForManager(1, 1000, {
            member_ids: [producerId],
          }).then((response) => {
            if (!response.error) {
              // Create a map of producer IDs to their mix seasons
              const seasonsByProducer = { ...producerMixSeasons };

              // Process the mix seasons and organize by producer
              response.list.forEach((season) => {
                const pid = season.user_id;

                // Only include active seasons (status === 1)
                if (season.status === 1) {
                  if (!seasonsByProducer[pid]) {
                    seasonsByProducer[pid] = [];
                  }

                  seasonsByProducer[pid].push(season);
                }
              });

              setProducerMixSeasons(seasonsByProducer);
            }
          });

          // Fetch mix types for this producer
          retrieveAllForMixTypeForManager(1, 1000, {
            member_ids: [producerId],
          }).then((response) => {
            if (!response.error) {
              // Create a map of producer IDs to their mix types
              const mixTypesByProducer = { ...producerMixTypes };

              response.list.forEach((mixType) => {
                const pid = mixType.user_id;
                if (!mixTypesByProducer[pid]) {
                  mixTypesByProducer[pid] = {
                    mixTypes: [],
                  };
                }
                mixTypesByProducer[pid].mixTypes.push(mixType);
              });

              setProducerMixTypes(mixTypesByProducer);
            }
          });
        }
      } else if (field === "status") {
        // Don't update the status directly, open the confirmation modal instead
        setSelectedItem(item);
        setNewStatus(value);
        setIsStatusModalOpen(true);
        return prev; // Return the previous state without changes
      } else {
        item[field] = value;
      }

      if (
        !item.isSpecialRow &&
        !item.isSpecial &&
        (field === "quantity" || field === "price" || field === "discount")
      ) {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply fixed discount to price
        const discountedPrice = Math.max(0, price - discount);

        item.amount = discountedPrice * quantity;
      }

      if (
        (item.isSpecialRow || item.isSpecial) &&
        (field === "price" || field === "discount")
      ) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        item.amount = Math.max(0, price - discount);
      }

      const subtotal = newItems.reduce(
        (sum, item) => sum + (item.amount || 0),
        0
      );
      const total = subtotal + subtotal * (prev.tax / 100);

      return {
        ...prev,
        items: newItems,
        subtotal,
        total,
      };
    });
  };

  // Function to save checks to invoice via PUT API
  const saveChecksToInvoice = async (updatedChecks) => {
    try {
      if (!selectedClientId) {
        throw new Error("No client selected");
      }

      const sdk = new MkdSDK();

      // Get current invoice data
      const currentInvoice = await sdk.getInvoiceById(id);
      if (currentInvoice.error) {
        throw new Error("Failed to fetch current invoice data");
      }

      // Find client details
      const client = clients.find(
        (client) => client.id.toString() === selectedClientId
      );

      if (!client) {
        throw new Error("Client information not found");
      }

      // Prepare the payload with current invoice data and updated checks
      const payload = {
        clientId: parseInt(selectedClientId),
        clientEmail: client.email || "",
        clientName: client.program || "",
        programName: client.program || "",
        invoiceDate: invoiceDates.invoiceDate,
        dueDate: invoiceDates.dueDate || null,
        items: currentInvoice.items,
        depositPercentage: invoiceData.depositPercentage,
        notes: invoiceData.notes,
        termsAndConditions: invoiceData.termsAndConditions,
        checks: JSON.stringify(updatedChecks), // Add the updated checks
      };

      // Update the invoice with the new checks
      const result = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${id}`,
        payload,
        "PUT"
      );

      if (result.error) {
        throw new Error(result.message || "Failed to save checks to invoice");
      }
    } catch (error) {
      console.error("Error saving checks to invoice:", error);
      throw error; // Re-throw to be handled by the calling function
    }
  };

  // Function to handle check upload
  const handleCheckUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "application/pdf",
    ];
    if (!allowedTypes.includes(file.type)) {
      showToast(
        globalDispatch,
        "Please upload only JPG, PNG, or PDF files",
        3000,
        "error"
      );
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      showToast(
        globalDispatch,
        "File size must be less than 10MB",
        3000,
        "error"
      );
      return;
    }

    try {
      setIsUploadingCheck(true);

      const formData = new FormData();
      formData.append("files", file);

      const result = await uploadS3FilesAPI(formData);

      if (!result.error && result.attachments) {
        const attachments = JSON.parse(result.attachments);
        const uploadedFile = attachments[0];

        const newCheck = {
          id: Date.now(), // Simple ID for frontend
          url: uploadedFile.url,
          filename: uploadedFile.filename,
          uploadDate: moment().format("YYYY-MM-DD HH:mm:ss"),
        };

        const updatedChecks = [...uploadedChecks, newCheck];
        setUploadedChecks(updatedChecks);

        // Automatically trigger invoice PUT API to save the checks
        await saveChecksToInvoice(updatedChecks);

        showToast(
          globalDispatch,
          "Check uploaded and saved successfully",
          3000,
          "success"
        );
      } else {
        throw new Error(result.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error uploading check:", error);
      showToast(globalDispatch, "Failed to upload check", 3000, "error");
    } finally {
      setIsUploadingCheck(false);
      // Reset the input
      event.target.value = "";
    }
  };

  // Function to delete a check with confirmation
  const handleDeleteCheck = async (checkId) => {
    const check = uploadedChecks.find((c) => c.id === checkId);
    if (!check) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete "${check.filename}"? This action cannot be undone.`
    );
    if (!confirmed) return;

    try {
      const updatedChecks = uploadedChecks.filter(
        (check) => check.id !== checkId
      );
      setUploadedChecks(updatedChecks);

      // Automatically trigger invoice PUT API to save the updated checks
      await saveChecksToInvoice(updatedChecks);

      showToast(
        globalDispatch,
        "Check removed and saved successfully",
        3000,
        "success"
      );
    } catch (error) {
      console.error("Error removing check:", error);
      showToast(globalDispatch, "Failed to remove check", 3000, "error");
      // Revert the state change if the API call failed
      setUploadedChecks(uploadedChecks);
    }
  };

  const handleUpdateInvoice = async () => {
    if (!selectedClientId) {
      showToast(globalDispatch, "Please select a client", 3000, "error");
      return;
    }

    // Find client details
    const client = clients.find(
      (client) => client.id.toString() === selectedClientId
    );

    if (!client) {
      showToast(globalDispatch, "Client information not found", 3000, "error");
      return;
    }

    // Format items with IDs for the API
    const formattedItems = invoiceData.items.map((item) => {
      if (item.isSpecialRow || item.isSpecial) {
        return {
          id: item.id, // Include ID for existing items
          price: parseFloat(item.price) || 0,
          quantity: parseInt(item.quantity) || 1,
          discount: parseFloat(item.discount) || 0,
          producer: "",
          producers: [],
          specialType: "additional",
          isSpecial: true,
          name: item.name || "Additional Charge",
          description: item.name || "Additional Charge", // Add description for special charges
          mixDate: "",
          teamName: "",
          division: "",
          musicSurveyDue: "",
          routineSubmissionDue: "",
          estimatedCompletion: "",
          mixSeasonId: item.mixSeason || "",
          mixTypeId: "",
        };
      } else {
        // Get the mix type name for the description
        // First try to find it in the mix types list
        const selectedMixType = mixTypes.find(
          (mt) => mt.id === parseInt(item.mixType)
        );

        // If found in mix types, use that name, otherwise use the existing description
        // which should already contain the mix type name
        let mixTypeName = "";
        if (selectedMixType) {
          mixTypeName = selectedMixType.name;
          console.log(
            `Found mix type name: ${mixTypeName} for mix type ID: ${item.mixType}`
          );
        } else {
          mixTypeName = item.description || "";
          console.log(
            `Using description as mix type name: ${mixTypeName} for mix type ID: ${item.mixType}`
          );
        }

        return {
          id: item.id, // Include ID for existing items
          price: parseFloat(item.price) || 0,
          quantity: parseInt(item.quantity) || 1,
          discount: parseFloat(item.discount) || 0,
          producer: item.producer || "",
          producers: [],
          specialType: "",
          isSpecial: false,
          description: mixTypeName || "", // Add description with just the mix type name
          mixDate: item.mixDate || "",
          teamName: item.teamName || "",
          division: item.division || "",
          musicSurveyDue: item.musicSurveyDue || "",
          routineSubmissionDue: item.routineSubmissionDue || "",
          estimatedCompletion: item.estimatedCompletion || "",
          mixSeasonId: item.mixSeason || "",
          mixTypeId: item.mixType || "",
        };
      }
    });

    // Convert deposit amount to cents if using fixed amount
    const depositAmount =
      invoiceData.depositPercentage > 0
        ? 0
        : Math.round(invoiceData.depositAmount * 100); // Convert to cents

    // Create the final payload
    const payload = {
      selectedClientId,
      invoiceDates,
      invoiceData: {
        ...invoiceData,
        items: formattedItems,
        depositAmount: depositAmount,
        checks: JSON.stringify(uploadedChecks), // Add checks as stringified array
      },
      isQuote,
    };

    console.log("Manager update invoice payload:", payload);
    onSubmit(payload);
  };

  return (
    <div className="rounded-lg bg-boxdark p-6">
      {/* Company Information Header */}
      <div className="mb-6 flex items-center justify-between rounded-lg bg-meta-4 p-4">
        <div className="flex items-center gap-4">
          {companyInfo?.company?.manager?.license_company_logo &&
          companyInfo.company.manager.license_company_logo !== "null" ? (
            <img
              src={companyInfo.company.manager.license_company_logo}
              alt={companyInfo.company.manager.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : companyInfo?.company?.main_member?.license_company_logo &&
            companyInfo.company.main_member.license_company_logo !== "null" ? (
            <img
              src={companyInfo.company.main_member.license_company_logo}
              alt={
                companyInfo.company.main_member.company_name || "Company Logo"
              }
              className="h-16 w-auto object-contain"
            />
          ) : userDetails?.company_logo &&
            userDetails.company_logo !== "null" ? (
            <img
              src={userDetails.company_logo}
              alt={userDetails?.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {companyInfo?.company?.manager?.company_name &&
              companyInfo.company.manager.company_name !== "null"
                ? companyInfo.company.manager.company_name
                : companyInfo?.company?.main_member?.company_name &&
                  companyInfo.company.main_member.company_name !== "null"
                ? companyInfo.company.main_member.company_name
                : userDetails?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {companyInfo?.company?.manager?.office_email &&
              companyInfo.company.manager.office_email !== "null"
                ? companyInfo.company.manager.office_email
                : companyInfo?.company?.main_member?.office_email &&
                  companyInfo.company.main_member.office_email !== "null"
                ? companyInfo.company.main_member.office_email
                : userDetails?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">INVOICE</h2>
        </div>
      </div>

      {/* Header with back button */}
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={onClose}
          className="flex items-center gap-2 text-white hover:text-primary"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Invoices
        </button>
        <h2 className="text-2xl font-bold text-white">Edit Invoice #{id}</h2>
        <button
          onClick={onResend}
          disabled={loading}
          className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 font-medium text-white hover:bg-opacity-90"
        >
          <FontAwesomeIcon icon="paper-plane" className="h-4 w-4" />
          Resend Link
        </button>
      </div>

      {/* Client Information */}
      <div className="mb-3 pb-2">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="flex items-center gap-4">
          <div className="w-1/3">
            <CustomSelect2
              value={selectedClientId || ""}
              onChange={(value) => setSelectedClientId(value)}
              className="h-8 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-3 py-1 text-sm text-white"
              disabled
            >
              <option value="">Select Client</option>
              {clients.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.program}
                </option>
              ))}
            </CustomSelect2>
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-3">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Producer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Team Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Division
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Survey Due / Submission Due / Est. Completion
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Discount ($)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Season
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoiceData.items.map((item, index) =>
                item.isSpecialRow || item.isSpecial ? (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 text-[12px] hover:bg-primary/5"
                  >
                    <td colSpan={2} className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.name}
                        onChange={(e) =>
                          handleItemChange(index, "name", e.target.value)
                        }
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        placeholder="Charge Name"
                      />
                    </td>
                    <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for spacing */}
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      {/* Empty cell for dates */}
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.discount || 0}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "discount",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for mix season column */}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        textClass="!text-[10px]"
                        value={item.status || "5"}
                        onChange={(value) =>
                          handleItemChange(index, "status", value)
                        }
                        className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 !text-[10px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                      >
                        <option value="3">Paid in Full</option>
                        <option value="2">Deposit Paid</option>
                        <option value="5">Unpaid</option>
                        <option value="1">Complete</option>
                        <option value="4">Awaiting Edit</option>
                      </CustomSelect2>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ) : (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 text-[12px] hover:bg-primary/5"
                  >
                    <td className="row-date2 whitespace-nowrap px-4 py-3">
                      <SingleDatePicker
                        id={`mixDate_${index}`}
                        date={item.mixDate ? moment(item.mixDate) : null}
                        onDateChange={(date) => {
                          handleItemChange(
                            index,
                            "mixDate",
                            date ? date.format("YYYY-MM-DD") : null
                          );
                          calculateDates(
                            date ? date.format("YYYY-MM-DD") : null,
                            index
                          );
                        }}
                        focused={focusedInput[`mixDate_${index}`]}
                        onFocusChange={({ focused }) =>
                          setFocusedInput((prev) => ({
                            ...prev,
                            [`mixDate_${index}`]: focused,
                          }))
                        }
                        numberOfMonths={1}
                        isOutsideRange={() => false}
                        displayFormat="MM-DD-YYYY"
                        placeholder="Select Mix Date"
                        readOnly={true}
                        customInputIcon={null}
                        noBorder={true}
                        block
                        className="w-full rounded border border-stroke/50"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        textClass="!text-[12px]"
                        value={item.producer}
                        onChange={(value) =>
                          handleItemChange(index, "producer", value)
                        }
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                      >
                        <option value="">Select Producer</option>
                        {producers.map((producer) => (
                          <option key={producer.value} value={producer.value}>
                            {producer.label}
                          </option>
                        ))}
                      </CustomSelect2>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        textClass="!text-[12px]"
                        value={item.mixType}
                        onChange={(value) => {
                          handleItemChange(index, "mixType", value);
                          const selectedMixType = mixTypes.find(
                            (mt) => mt.id === parseInt(value)
                          );
                          if (selectedMixType) {
                            // Set the price if available
                            handleItemChange(
                              index,
                              "price",
                              selectedMixType.price
                            );

                            // Set the description field with just the mix type name
                            // This is what will be stored in the backend
                            console.log(
                              `Setting description to mix type name: ${selectedMixType.name}`
                            );
                            handleItemChange(
                              index,
                              "description",
                              selectedMixType.name || ""
                            );
                          }
                        }}
                        className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                      >
                        <option value="">Select Mix Type</option>
                        {item.producer &&
                          getAvailableMixTypes(item.producer).map((mixType) => (
                            <option key={mixType.id} value={mixType.id}>
                              {mixType.name} - ${mixType.price}
                            </option>
                          ))}
                      </CustomSelect2>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.teamName}
                        onChange={(e) =>
                          handleItemChange(index, "teamName", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value.startsWith("Team ")) {
                            handleItemChange(index, "teamName", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.division}
                        onChange={(e) =>
                          handleItemChange(index, "division", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value === "TBD") {
                            handleItemChange(index, "division", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex flex-col gap-2">
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`musicSurveyDue_${index}`}
                            date={
                              item.musicSurveyDue
                                ? moment(item.musicSurveyDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "musicSurveyDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={focusedInput[`musicSurveyDue_${index}`]}
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`musicSurveyDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`routineSubmissionDue_${index}`}
                            date={
                              item.routineSubmissionDue
                                ? moment(item.routineSubmissionDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "routineSubmissionDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`routineSubmissionDue_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`routineSubmissionDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`estimatedCompletion_${index}`}
                            date={
                              item.estimatedCompletion
                                ? moment(item.estimatedCompletion)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "estimatedCompletion",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`estimatedCompletion_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`estimatedCompletion_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Completion Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                      </div>
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.discount || 0}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "discount",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        max="100"
                        placeholder="0%"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <div className="flex items-center gap-1">
                        <CustomSelect2
                          value={item.mixSeason}
                          onChange={(value) =>
                            handleItemChange(index, "mixSeason", value)
                          }
                          className="w-full rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        >
                          <option value="">Select Season</option>
                          {mixSeasons.map((season) => (
                            <option key={season.id} value={season.id}>
                              {season.name}
                            </option>
                          ))}
                        </CustomSelect2>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        textClass="!text-[10px]"
                        value={item.status || "5"}
                        onChange={(value) =>
                          handleItemChange(index, "status", value)
                        }
                        className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 !text-[10px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                      >
                        <option value="3">Paid in Full</option>
                        <option value="2">Deposit Paid</option>
                        <option value="5">Unpaid</option>
                        <option value="1">Complete</option>
                        <option value="4">Awaiting Edit</option>
                      </CustomSelect2>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
        <div className="mt-2 flex justify-end gap-2">
          <button
            onClick={handleAddSpecialRow}
            className="flex items-center gap-2 rounded bg-meta-5 px-4 py-1 text-sm font-medium text-white hover:bg-meta-5/80"
          >
            <PlusCircle className="h-5 w-5" />
            Add Special Charge
          </button>
          <button
            onClick={handleAddItem}
            className="flex items-center gap-2 rounded bg-primary px-4 py-1 text-sm font-medium text-white hover:bg-primary/80"
          >
            <Plus className="h-5 w-5" />
            Add Row
          </button>
        </div>
      </div>

      {/* Deposit and Terms Section */}
      <div className="mt-6 grid grid-cols-3 gap-6">
        {/* Left Column - Deposit Settings */}
        <div className="space-y-2">
          <h3 className="text-base font-semibold text-white">
            Deposit Settings
          </h3>
          <div className="space-y-2">
            <div>
              <label className="mb-1.5 block text-sm text-white">Type</label>
              <CustomSelect2
                value={
                  invoiceData.depositPercentage > 0 ? "percentage" : "amount"
                }
                onChange={(value) => {
                  const isAmount = value === "amount";
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isAmount ? 0 : 0,
                    depositPercentage: isAmount
                      ? 0
                      : prev.depositPercentage ||
                        userDetails?.deposit_percent ||
                        30,
                  }));
                }}
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
              >
                <option value="amount">Fixed Amount</option>
                <option value="percentage">Percentage</option>
              </CustomSelect2>
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                {invoiceData.depositPercentage > 0
                  ? "Percentage (%)"
                  : "Amount ($)"}
              </label>
              <input
                type="number"
                value={
                  invoiceData.depositPercentage > 0
                    ? invoiceData.depositPercentage
                    : invoiceData.depositAmount
                }
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  const isPercentage = invoiceData.depositPercentage > 0;
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isPercentage ? 0 : value,
                    depositPercentage: isPercentage ? value : 0,
                  }));
                }}
                placeholder={
                  invoiceData.depositPercentage > 0
                    ? `Default: ${userDetails?.deposit_percent || 30}%`
                    : "Enter amount"
                }
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
                min="0"
                max={invoiceData.depositPercentage > 0 ? 100 : undefined}
              />
            </div>
          </div>
        </div>

        {/* Middle and Right Columns - Notes and Terms */}
        <div className="col-span-2 space-y-2">
          <h3 className="text-base font-semibold text-white">Notes & Terms</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="mb-1.5 block text-sm text-white">Notes</label>
              <textarea
                value={invoiceData.notes}
                onChange={(e) =>
                  setInvoiceData((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="h-[110px] w-full rounded border border-stroke bg-transparent px-3 py-2 text-sm text-white"
                rows="4"
                placeholder="Add any additional notes..."
              />
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                Terms and Conditions
              </label>
              <SunEditor
                setContents={invoiceData.termsAndConditions}
                onChange={(content) =>
                  setInvoiceData((prev) => ({
                    ...prev,
                    termsAndConditions: content,
                  }))
                }
                getSunEditorInstance={getSunEditorInstance}
                setOptions={{
                  buttonList: buttonList.complex,
                  height: 110,
                  width: "100%",
                  placeholder: userDetails?.contract_agreement
                    ? "Terms and conditions loaded from your settings"
                    : "Add terms and conditions...",
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Check Upload Section */}
      <div className="mt-6 rounded-lg bg-meta-4/20 p-4">
        <h3 className="mb-4 text-base font-semibold text-white">
          Check Uploads
        </h3>

        {/* Upload Area */}
        <div className="mb-4">
          <label className="mb-2 block text-sm text-white">
            Upload Check Images/PDFs
          </label>
          <div className="flex items-center gap-4">
            <input
              type="file"
              accept=".jpg,.jpeg,.png,.pdf"
              onChange={handleCheckUpload}
              className="hidden"
              id="check-upload"
              disabled={isUploadingCheck}
            />
            <label
              htmlFor="check-upload"
              className={`flex cursor-pointer items-center gap-2 rounded bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-opacity-90 ${
                isUploadingCheck ? "cursor-not-allowed opacity-50" : ""
              }`}
            >
              <Upload className="h-4 w-4" />
              {isUploadingCheck ? "Uploading..." : "Upload Check"}
            </label>
            <span className="text-xs text-bodydark2">
              Supported: JPG, PNG, PDF (Max 10MB)
            </span>
          </div>
        </div>

        {/* Uploaded Checks List */}
        {uploadedChecks.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white">Uploaded Checks:</h4>
            <div className="space-y-2">
              {uploadedChecks.map((check) => (
                <div
                  key={check.id}
                  className="flex items-center justify-between rounded border border-stroke/50 bg-boxdark-2/40 p-3"
                >
                  <div className="flex items-center gap-3">
                    <FileText className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-sm font-medium text-white">
                        {check.filename}
                      </p>
                      <p className="text-xs text-bodydark2">
                        Uploaded:{" "}
                        {moment(check.uploadDate).format("MMM DD, YYYY HH:mm")}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <a
                      href={check.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 rounded bg-meta-5 px-3 py-1 text-xs font-medium text-white hover:bg-opacity-90"
                    >
                      <FileText className="h-3 w-3" />
                      View
                    </a>
                    <button
                      onClick={() => handleDeleteCheck(check.id)}
                      className="flex items-center gap-1 rounded bg-danger px-3 py-1 text-xs font-medium text-white hover:bg-opacity-90"
                      title="Delete check"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="mt-6 flex justify-end gap-4">
        <button
          onClick={onClose}
          className="rounded-lg border border-stroke bg-opacity-80 px-6 py-2 text-bodydark"
        >
          Cancel
        </button>

        {/* Add Save as Quote button */}
        <PDFDownloadLink
          document={
            <InvoiceQuotePDF
              data={{
                selectedClientId,
                newClientData: {},
                invoiceDates,
                invoiceData: { ...invoiceData, isQuote: true },
                clientDetails:
                  clients.find(
                    (client) => client.id === parseInt(selectedClientId)
                  ) || {},
              }}
              userDetails={userDetails}
              companyInfo={companyInfo}
            />
          }
          fileName={`quote-${moment().format("YYYY-MM-DD")}.pdf`}
          className={`flex items-center gap-2 rounded-lg bg-meta-5 px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50 ${
            loading ? "cursor-not-allowed opacity-50" : ""
          }`}
          onClick={() => {
            if (!selectedClientId) {
              showToast(
                globalDispatch,
                "Please select a client",
                3000,
                "error"
              );
              return false;
            }
            return true;
          }}
        >
          {({ loading: pdfLoading }) => (
            <>
              <Download className="h-4 w-4" />
              {pdfLoading ? "Generating Quote..." : "Save as Quote"}
            </>
          )}
        </PDFDownloadLink>

        <button
          onClick={() => {
            setIsQuote(false);
            handleUpdateInvoice();
          }}
          disabled={loading}
          className="rounded-lg bg-primary px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50"
        >
          {loading ? "Updating..." : "Update Invoice"}
        </button>
      </div>

      {/* Status Confirmation Modal */}
      <StatusConfirmationModal
        isOpen={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        status={newStatus}
        onConfirm={() => {
          if (selectedItem && selectedItem.id && newStatus) {
            handleStatusUpdate(selectedItem.id, newStatus);
          }
        }}
      />
    </div>
  );
};

export default ManagerEditInvoiceComponent;
