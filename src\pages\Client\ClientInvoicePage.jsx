import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import { FileText, Download, ArrowLeft, CreditCard } from "lucide-react";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import { useNavigate } from "react-router-dom";
import { loadStripe } from "@stripe/stripe-js";
import { Elements, CardElement } from "@stripe/react-stripe-js";
import InvoiceMultiStep, { STEPS } from "Components/Invoice/InvoiceMultiStep";
import SuccessModal from "Components/SuccessModal";

const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);

const ClientInvoicePage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(1);
  const [dataTotal, setDataTotal] = useState(0);
  const pageSize = 10;
  const navigate = useNavigate();

  // Payment modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [invoiceData, setInvoiceData] = useState(null);
  const [clientData, setClientData] = useState(null);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [step2Info, setStep2Info] = useState({
    feedback_notes: "",
    signature: "",
    initial: "",
  });
  const [legalDocuments, setLegalDocuments] = useState({
    termsOfService: "",
    privacyPolicy: "",
  });

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: { path: "invoices" },
    });
    fetchInvoices();
  }, [currentPage]);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const clientId = localStorage.getItem("userClientId");

      // Use TreeQL to get paginated invoices for the client
      const result = await tdk.getPaginate("invoice", {
        page: currentPage,
        size: pageSize,
        filter: [`client_id,eq,${clientId}`],
      });

      if (result) {
        setInvoices(result.list || []);
        setPageCount(result.num_pages || 1);
        setDataTotal(result.total || 0);
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      tokenExpireError(dispatch, error.message);
      showToast(globalDispatch, "Failed to fetch invoices", "error");
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
        return "bg-success/10 text-success";
      case "pending":
        return "bg-warning/10 text-warning";
      case "overdue":
        return "bg-danger/10 text-danger";
      default:
        return "bg-primary/10 text-primary";
    }
  };

  const fetchInvoiceDetails = async (invoiceId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${invoiceId}`,
        [],
        "GET"
      );

      if (!response.error) {
        setInvoiceData(response);
        setStep2Info((prev) => ({
          ...prev,
          feedback_notes: response?.invoice?.feedback_notes,
          signature: response?.invoice?.signature,
          initial: response?.invoice?.initials,
        }));

        if (response?.invoice?.client_id) {
          await fetchClientDetails(response?.invoice?.client_id);
        }

        await fetchLegalDocuments();
        return response;
      } else {
        showToast(globalDispatch, "Failed to fetch invoice details", "error");
        return null;
      }
    } catch (error) {
      console.error("Error fetching invoice details:", error);
      showToast(globalDispatch, "Failed to fetch invoice details", "error");
      return null;
    }
  };

  const fetchClientDetails = async (clientId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/client/${clientId}`,
        [],
        "GET"
      );

      if (!response.error) {
        setClientData(response?.model);
      } else {
        console.error("Error fetching client details:", response.message);
      }
    } catch (err) {
      console.error("Error fetching client details:", err);
    }
  };

  const fetchLegalDocuments = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/legal-documents",
        [],
        "GET"
      );

      if (!response.error && response.data && response.data.length > 0) {
        const legalDoc = response.data[0];
        setLegalDocuments({
          termsOfService: legalDoc.terms_of_service || "",
          privacyPolicy: legalDoc.privacy_policy || "",
        });
      } else {
        console.error("Error fetching legal documents:", response.message);
      }
    } catch (err) {
      console.error("Error fetching legal documents:", err);
    }
  };

  const handlePayAllClick = async (invoice) => {
    setSelectedInvoice(invoice);
    const invoiceDetails = await fetchInvoiceDetails(invoice.id);
    if (invoiceDetails) {
      setShowPaymentModal(true);
    }
  };

  const updateClientDetails = async (clientId, clientData) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/client/${clientId}`,
        clientData,
        "PUT"
      );

      if (!response.error) {
        showToast(globalDispatch, "Client details updated successfully");
        return true;
      } else {
        showToast(globalDispatch, "Failed to update client details", "error");
        return false;
      }
    } catch (err) {
      showToast(globalDispatch, "Failed to update client details", "error");
      return false;
    }
  };

  const handleMultiStepComplete = async (data) => {
    try {
      const sdk = new MkdSDK();

      // Handle client details update
      if (data.step === STEPS.CLIENT_DETAILS) {
        if (data.clientDetails && invoiceData?.invoice?.client_id) {
          const clientUpdateSuccess = await updateClientDetails(
            invoiceData.invoice.client_id,
            data.clientDetails
          );
          if (!clientUpdateSuccess) {
            return;
          }
        }
        return;
      }

      // Handle signature and service agreement update
      if (data.step === STEPS.SIGN_REVIEW_SERVICE) {
        if (data.signature || data.initial || data.feedback) {
          await sdk.callRawAPI(
            `/v3/api/custom/equality_record/subscription/invoice/${selectedInvoice.id}`,
            {
              signature: data.signature,
              initial: data.initial,
              feedback_notes: data.feedback,
            },
            "PUT"
          );
        }
        return;
      }

      if (data.type === "payment") {
        console.log("data.payload", data.payload);

        try {
          // Get the producer name from the first item's producers array if available
          let producerName = "";
          if (
            invoiceData?.items?.length > 0 &&
            invoiceData.items[0].producers?.length > 0
          ) {
            producerName = invoiceData.items[0].producers[0];
          }

          // Update the payload to include the producer name
          const updatedPayload = {
            ...data.payload,
            producerName: producerName,
          };

          // Handle payment based on payment method
          if (data.payload.paymentMethod === "stripe") {
            // Process Stripe payment
            const response = await sdk.callRawAPI(
              `/v3/api/custom/equality_record/subscription/payment`,
              updatedPayload,
              "POST"
            );

            if (response.error) {
              showToast(
                globalDispatch,
                "Payment failed: " + response.message,
                "error"
              );
              return;
            }

            const { client_secret, payment_id } = response;

            const cardElement = data.card.elements.getElement(CardElement);
            if (!cardElement) {
              showToast(globalDispatch, "Card element not found", "error");
              return;
            }

            const { error, paymentIntent } =
              await data.card.stripe.confirmCardPayment(client_secret, {
                payment_method: {
                  card: cardElement,
                  billing_details: {
                    email: data.payload.email,
                    name: `${data.payload.firstName} ${data.payload.lastName}`,
                    phone: data.payload.phone,
                  },
                },
              });

            if (error) {
              showToast(
                globalDispatch,
                "Payment confirmation failed: " + error.message,
                4000,
                "error"
              );
              return;
            }

            const confirmResponse = await sdk.callRawAPI(
              `/v3/api/custom/equality_record/subscription/payment/confirm`,
              { paymentId: payment_id },
              "POST"
            );

            if (confirmResponse?.error) {
              showToast(
                globalDispatch,
                "Payment confirmation failed: " + confirmResponse.message,
                4000,
                "error"
              );
              return;
            }
          } else if (data.payload.paymentMethod === "check") {
            // Process check payment
            const response = await sdk.callRawAPI(
              `/v3/api/custom/equality_record/subscription/payment`,
              updatedPayload,
              "POST"
            );

            if (response.error) {
              showToast(
                globalDispatch,
                "Payment failed: " + response.message,
                "error"
              );
              return;
            }
          }

          showToast(globalDispatch, "Payment successful", 4000, "success");
          setShowPaymentModal(false);
          setIsSuccessModalOpen(true);
          // Refresh the invoices list
          fetchInvoices();
        } catch (err) {
          console.error("Payment processing error:", err);
          showToast(
            globalDispatch,
            "Payment failed. Please try again.",
            4000,
            "error"
          );
          return;
        }
      }
    } catch (err) {
      console.error("Error processing request:", err);
      showToast(
        globalDispatch,
        "An error occurred. Please try again.",
        4000,
        "error"
      );
    }
  };

  return (
    <div className="max-w-screen mx-auto p-4 md:p-6 2xl:p-10">
      <div className="shadow-default mb-5 rounded border border-stroke/50 bg-boxdark">
        <div className="mb-6 flex items-center justify-between px-4 py-5 md:px-6 2xl:px-9">
          <div>
            <h2 className="text-2xl font-bold text-white">My Invoices</h2>
            <p className="mt-1 text-lg text-bodydark">
              View and manage your invoices
            </p>
          </div>
        </div>
      </div>

      <div className="shadow-default rounded border border-strokedark bg-boxdark">
        <div className="flex items-center justify-between border-b border-strokedark px-4 md:px-6 2xl:px-9">
          <h4 className="my-3 text-2xl font-semibold text-white">Invoices</h4>
        </div>

        <div className="p-4 md:p-6 2xl:p-10">
          <div className="custom-overflow text-[12p min-h-[140px] overflow-x-auto">
            <table className="w-full table-auto">
              <thead className="bg-meta-4">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Invoice ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Date
                  </th>

                  <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Total
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Status
                  </th>
                  {/* <th className="px-4 py-3 text-xs font-medium tracking-wider text-center uppercase text-bodydark1">
                    Payment Status
                  </th> */}
                  <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Payment Date
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Actions
                  </th>
                </tr>
              </thead>
              {!loading && invoices.length > 0 ? (
                <tbody className="text-white">
                  {invoices.map((invoice) => (
                    <tr
                      key={invoice.id}
                      className="cursor-pointer border-b border-strokedark text-xs hover:bg-primary/5"
                      onClick={() => navigate(`/client/invoice/${invoice.id}`)}
                    >
                      <td className="whitespace-nowrap px-4 py-4">
                        #{invoice.id}
                      </td>
                      <td className="whitespace-nowrap px-4 py-4">
                        {moment(
                          new Date(invoice.create_at || invoice.created_at)
                        ).format("MMM DD, YYYY")}
                      </td>

                      <td className="whitespace-nowrap px-4 py-4 text-right">
                        ${parseFloat(invoice.total || 0).toFixed(2)}
                      </td>
                      <td className="whitespace-nowrap px-4 py-4">
                        <div className="flex justify-center">
                          <span
                            className={`inline-block rounded px-2.5 py-0.5 text-sm font-medium ${getStatusClass(
                              invoice.status
                            )}`}
                          >
                            {invoice.status || "pending"}
                          </span>
                        </div>
                      </td>
                      {/* <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex justify-center">
                          <span
                            className={`inline-block rounded px-2.5 py-0.5 text-sm font-medium ${
                              invoice.payment_status === "succeeded"
                                ? "bg-success/10 text-success"
                                : "bg-warning/10 text-warning"
                            }`}
                          >
                            {invoice.payment_status || "Not Paid"}
                          </span>
                        </div>
                      </td> */}
                      <td className="whitespace-nowrap px-4 py-4 text-center">
                        {invoice.payment_date
                          ? moment(new Date(invoice.payment_date)).format(
                              "MMM DD, YYYY"
                            )
                          : "N/A"}
                      </td>
                      <td className="whitespace-nowrap px-4 py-4">
                        <div className="flex items-center justify-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/client/invoice/${invoice.id}`);
                            }}
                            className="bg-primary px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-90"
                          >
                            View
                          </button>
                          {/* Show Pay All button only if invoice has remaining balance */}
                          {parseFloat(invoice.total || 0) >
                            parseFloat(invoice.payment_amount || 0) / 100 && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePayAllClick(invoice);
                              }}
                              className="flex items-center gap-1 bg-success px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-90"
                            >
                              <CreditCard className="h-3 w-3" />
                              Pay All
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              ) : loading ? (
                <tbody>
                  <tr>
                    <td colSpan={8} className="text-center">
                      <div className="flex h-[140px] items-center justify-center">
                        <ClipLoader color="#fff" size={30} />
                      </div>
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody>
                  <tr>
                    <td colSpan={8} className="text-center">
                      <div className="flex h-[140px] items-center justify-center">
                        <span className="text-bodydark">No invoices found</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              )}
            </table>
          </div>

          {pageCount > 1 && (
            <div className="flex items-center justify-between px-4 py-5">
              <div className="flex items-center gap-4">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="flex items-center justify-center rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90 disabled:bg-opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, pageCount))
                  }
                  disabled={currentPage === pageCount}
                  className="flex items-center justify-center rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90 disabled:bg-opacity-50"
                >
                  Next
                </button>
              </div>
              <span className="text-sm text-bodydark2">
                Page {currentPage} of {pageCount} ({dataTotal} total)
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && invoiceData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="max-h-[90vh] w-full max-w-4xl overflow-y-auto rounded-lg bg-boxdark p-6">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">
                Pay Invoice #{selectedInvoice?.id}
              </h2>
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setInvoiceData(null);
                  setClientData(null);
                }}
                className="text-bodydark2 hover:text-white"
              >
                ✕
              </button>
            </div>
            <Elements stripe={stripePromise}>
              <InvoiceMultiStep
                step2Info={step2Info}
                setStep2Info={setStep2Info}
                invoiceData={invoiceData}
                onSubmit={handleMultiStepComplete}
                termsContent={invoiceData?.invoice?.terms_and_conditions || ""}
                serviceAgreementContent={
                  invoiceData?.invoice?.terms_and_conditions || ""
                }
                clientData={clientData}
                companyInfo={invoiceData?.company_info}
                legalDocuments={legalDocuments}
              />
            </Elements>
          </div>
        </div>
      )}

      {/* Success Modal */}
      <SuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => {
          setIsSuccessModalOpen(false);
        }}
        message="Your payment was successful! The invoice has been updated."
      />
    </div>
  );
};

export default ClientInvoicePage;
